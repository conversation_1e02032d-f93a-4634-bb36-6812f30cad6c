<template>
  <div class="daily-stats-first-amount-container">
    <!-- 查询条件 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="queryParams" class="query-form">
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
            @change="handleDateChange"
            style="width: 300px;"
          />
        </el-form-item>
        <el-form-item label="支付账户">
            <el-select v-model="queryParams.accountName" placeholder="请选择支付账户">
            <el-option label="全部" value="all" />
            <el-option
              v-for="item in accountOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getFirstAmountData">查询</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 总金额展示 -->
    <el-card class="total-amount-card" shadow="hover">
      <div class="card-content-flex">
        <div class="stat-item">
          <div class="total-amount-title">总首次付款金额</div>
          <div class="total-amount-value">${{ formatAmount(totalAmount) }}</div>
        </div>
        <div class="stat-item">
          <div class="total-count-title">总订单数</div>
          <div class="total-count-value">{{ totalCount }}</div>
        </div>
        <div class="stat-item">
          <div class="total-rate-title">总重复率</div>
          <div class="total-rate-value">{{ formatRate(totalRate) }}%</div>
        </div>
        <div class="stat-item">
          <div class="total-rate-detail-title">重复数/订单数</div>
          <div class="total-rate-detail-value">{{ totalTargetCnt }} / {{ totalDayCnt }}</div>
        </div>
        <div class="stat-item">
          <div class="total-cancel-rate-title">总退订率</div>
          <div class="total-cancel-rate-value">{{ formatRate(totalCancelRate) }}%</div>
        </div>
        <div class="stat-item">
          <div class="total-cancel-amount-title">总退订金额</div>
          <div class="total-cancel-amount-value">${{ formatAmount(totalCancelAmount) }}</div>
        </div>
        <div class="stat-item">
          <div class="total-cancel-count-title">退订数/订单数</div>
          <div class="total-cancel-count-value">{{ totalCancelCount }} / {{ totalDayCnt }}</div>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-table-card">
      <div class="table-container">
        <el-table
          :data="mergedTableData"
          border
          stripe
          v-loading="loading"
        >
          <el-table-column prop="date" label="日期" align="center" sortable width="120" />
          <el-table-column prop="amount" label="首次付款金额" align="center" sortable width="150">
            <template #default="scope">
              ${{ formatAmount(scope.row.amount || 0) }}
            </template>
          </el-table-column>
          <el-table-column prop="count" label="订单数" align="center" sortable width="120" />
          <el-table-column prop="targetCnt" label="重复数" align="center" sortable width="120" />
          <!-- <el-table-column prop="dayCnt" label="总订单数" align="center" sortable width="120" /> -->
          <el-table-column label="重复率" align="center" sortable width="120" :sort-method="sortByDupRate">
            <template #default="scope">
              {{ scope.row.dayCnt > 0 ? formatRate((scope.row.targetCnt / scope.row.dayCnt) * 100) : '0.00' }}%
            </template>
          </el-table-column>
          <el-table-column prop="cancelCount" label="退订数" align="center" sortable width="120" />
          <el-table-column prop="cancelAmount" label="退订金额" align="center" sortable width="120">
            <template #default="scope">
              ${{ formatAmount(scope.row.cancelAmount || 0) }}
            </template>
          </el-table-column>
          <el-table-column label="退订率" align="center" sortable width="120" :sort-method="sortByCancelRate">
            <template #default="scope">
              {{ scope.row.count > 0 ? formatRate((scope.row.cancelCount / scope.row.count) * 100) : '0.00' }}%
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { getFirstAmountSummary } from '@/api/management/dailyStats'
import { getPaymentAccountNames } from '@/api/management/paymentAccounts'
import { ElMessage } from 'element-plus'

// 转换为-8时区的工具函数
const convertToUTC8 = (date) => {
  const utc = date.getTime() + date.getTimezoneOffset() * 60000
  return new Date(utc - (8 * 60 * 60 * 1000))
}

// 日期范围快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      return [targetToday, targetToday]
    }
  },
  {
    text: '昨天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const yesterday = new Date(targetToday)
      yesterday.setDate(targetToday.getDate() - 1)
      return [yesterday, yesterday]
    }
  },
  {
    text: '最近3天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday)
      start.setDate(targetToday.getDate() - 2)
      return [start, targetToday]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday)
      start.setDate(targetToday.getDate() - 6)
      return [start, targetToday]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday)
      start.setDate(targetToday.getDate() - 29)
      return [start, targetToday]
    }
  },
  {
    text: '当月',
    value: () => {
      const targetToday = convertToUTC8(new Date())
      const start = new Date(targetToday.getFullYear(), targetToday.getMonth(), 1)
      return [start, targetToday]
    }
  }
]

// 支付账户选项
const accountOptions = ref([])

// 获取支付账户列表
const getAccountOptions = async () => {
  try {
    const { data } = await getPaymentAccountNames()
    if (data && Array.isArray(data)) {
      accountOptions.value = data.map(name => ({
        label: name,
        value: name
      }))
    }
  } catch (error) {
    console.error('获取支付账户列表失败:', error)
    ElMessage.error('获取支付账户列表失败')
  }
}

// 页面加载时获取支付账户列表
onMounted(() => {
  getAccountOptions()
})

// 查询参数
const queryParams = ref({
  bgnDate: '',
  endDate: '',
  accountName: ''
})

// 日期范围
const dateRange = ref([])

// 金额数据
const amountTableData = ref([])
const totalAmount = ref(0)
const totalCount = ref(0)

// 退订数据
const cancelTableData = ref([])
const totalCancelAmount = ref(0)
const totalCancelCount = ref(0)

// 重复率数据
const dupRateTableData = ref([])
const totalTargetCnt = ref(0)
const totalDayCnt = ref(0)
const totalRate = computed(() => {
  return totalDayCnt.value > 0 ? (totalTargetCnt.value / totalDayCnt.value) * 100 : 0
})

// 总退订率
const totalCancelRate = computed(() => {
  return totalCount.value > 0 ? (totalCancelCount.value / totalCount.value) * 100 : 0
})

// 合并表格数据
const mergedTableData = computed(() => {
  const dataMap = new Map()

  // 处理金额数据
  amountTableData.value.forEach(item => {
    dataMap.set(item.date, {
      date: item.date,
      amount: item.amount,
      count: item.count,
      cancelAmount: 0,
      cancelCount: 0,
      targetCnt: 0,
      dayCnt: 0
    })
  })

  // 处理退订数据并合并
  cancelTableData.value.forEach(item => {
    if (dataMap.has(item.date)) {
      const existing = dataMap.get(item.date)
      existing.cancelAmount = item.amount
      existing.cancelCount = item.count
    } else {
      dataMap.set(item.date, {
        date: item.date,
        amount: 0,
        count: 0,
        cancelAmount: item.amount,
        cancelCount: item.count,
        targetCnt: 0,
        dayCnt: 0
      })
    }
  })

  // 处理重复率数据并合并
  dupRateTableData.value.forEach(item => {
    if (dataMap.has(item.date)) {
      const existing = dataMap.get(item.date)
      existing.targetCnt = item.targetCnt
      existing.dayCnt = item.dayCnt
    } else {
      dataMap.set(item.date, {
        date: item.date,
        amount: 0,
        count: 0,
        cancelAmount: 0,
        cancelCount: 0,
        targetCnt: item.targetCnt,
        dayCnt: item.dayCnt
      })
    }
  })

  // 转换为数组并按日期排序
  return Array.from(dataMap.values()).sort((a, b) => {
    return new Date(a.date) - new Date(b.date)
  })
})

const loading = ref(false)

// 处理日期变化
const handleDateChange = (val) => {
  if (val) {
    queryParams.value.bgnDate = val[0]
    queryParams.value.endDate = val[1]
  } else {
    queryParams.value.bgnDate = ''
    queryParams.value.endDate = ''
  }
}

// 格式化金额
const formatAmount = (amount) => {
  return parseFloat(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化比率
const formatRate = (rate) => {
  return parseFloat(rate).toFixed(2)
}

// 重复率排序方法
const sortByDupRate = (a, b) => {
  const rateA = a.dayCnt > 0 ? (a.targetCnt / a.dayCnt) * 100 : 0
  const rateB = b.dayCnt > 0 ? (b.targetCnt / b.dayCnt) * 100 : 0
  return rateA - rateB
}

// 退订率排序方法
const sortByCancelRate = (a, b) => {
  const rateA = a.count > 0 ? (a.cancelCount / a.count) * 100 : 0
  const rateB = b.count > 0 ? (b.cancelCount / b.count) * 100 : 0
  return rateA - rateB
}

// 获取首次付款金额数据
const getFirstAmountData = async () => {
  if (!queryParams.value.bgnDate || !queryParams.value.endDate) {
    ElMessage.warning('请选择日期范围')
    return
  }

  // 创建一个新的查询对象，避免修改原始值
  const queryData = { ...queryParams.value }

  // 如果选择了"全部"，则在发送请求时将accountName设置为空字符串
  if (queryData.accountName === 'all') {
    queryData.accountName = ''
  }

  loading.value = true
  try {
    const { data } = await getFirstAmountSummary(queryData)
    if (data) {
      // 处理首次付款金额数据
      if (data.firstAmountSummary) {
        amountTableData.value = data.firstAmountSummary.dailyAmountCounts || []
        totalAmount.value = data.firstAmountSummary.totalAmount || 0
        totalCount.value = data.firstAmountSummary.totalCount || 0
      }

      // 处理退订数据
      if (data.cancelSummary) {
        cancelTableData.value = data.cancelSummary.dailyAmountCounts || []
        totalCancelAmount.value = data.cancelSummary.totalAmount || 0
        totalCancelCount.value = data.cancelSummary.totalCount || 0
      }

      // 处理重复率数据
      if (data.paymentDupRateSummary) {
        dupRateTableData.value = data.paymentDupRateSummary.dailyRates || []
        totalTargetCnt.value = data.paymentDupRateSummary.totalTargetCnt || 0
        totalDayCnt.value = data.paymentDupRateSummary.totalDayCnt || 0
      }
    }
  } catch (error) {
    console.error('获取首次付款金额数据失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 页面加载时设置默认日期为最近7天
onMounted(() => {
  const end = convertToUTC8(new Date())
  const start = new Date(end)
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)

  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  dateRange.value = [formatDate(start), formatDate(end)]
  queryParams.value.bgnDate = dateRange.value[0]
  queryParams.value.endDate = dateRange.value[1]
  queryParams.value.accountName = 'all'
  getFirstAmountData()
})
</script>

<style scoped>
.daily-stats-first-amount-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.total-amount-card {
  margin-bottom: 20px;
  text-align: center;
  padding: 20px;
}

.card-content-flex {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
}

.stat-item {
  flex: 1;
  padding: 0 10px;
  max-width: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.total-amount-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-amount-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
  margin-bottom: 0;
}

.total-count-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-count-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-rate-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-rate-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-rate-detail-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-rate-detail-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-cancel-amount-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-cancel-amount-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-cancel-count-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-cancel-count-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.total-cancel-rate-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.total-cancel-rate-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  height: 40px;
  line-height: 40px;
}

.data-table-card {
  margin-bottom: 20px;
}

.table-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.table-container .el-table {
  max-width: 1000px;
  width: auto;
  margin: 0 auto;
}
</style>
